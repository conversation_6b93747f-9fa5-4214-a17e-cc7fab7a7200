import { NextRequest, NextResponse } from 'next/server';
import { getTemplateById, updateTemplate, deleteTemplate } from '@/lib/database';
import { unlink, rmdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

/**
 * GET /api/templates/[id] - Get a specific template by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const templateId = parseInt(params.id);
    
    if (isNaN(templateId)) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      );
    }

    const template = await getTemplateById(templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Parse placeholders from JSON string to array
    const templateWithParsedPlaceholders = {
      ...template,
      placeholders: template.placeholders ? JSON.parse(template.placeholders) : []
    };

    return NextResponse.json({ template: templateWithParsedPlaceholders });
  } catch (error) {
    console.error('Error fetching template:', error);
    return NextResponse.json(
      { error: 'Failed to fetch template' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/templates/[id] - Update a template
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const templateId = parseInt(params.id);
    
    if (isNaN(templateId)) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { description, layout_size } = body;

    // Validate required fields (we only allow editing description and layout_size)
    if (description === undefined && layout_size === undefined) {
      return NextResponse.json(
        { error: 'At least one field (description or layout_size) must be provided' },
        { status: 400 }
      );
    }

    // Get the existing template to preserve other fields
    const existingTemplate = await getTemplateById(templateId);
    
    if (!existingTemplate) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Update the template with only the allowed fields
    await updateTemplate(
      templateId,
      existingTemplate.template_name, // Keep existing name
      description !== undefined ? description : existingTemplate.description,
      existingTemplate.placeholders, // Keep existing placeholders
      layout_size !== undefined ? layout_size : existingTemplate.layout_size
    );

    // Fetch the updated template to return
    const updatedTemplate = await getTemplateById(templateId);
    const templateWithParsedPlaceholders = {
      ...updatedTemplate,
      placeholders: updatedTemplate?.placeholders ? JSON.parse(updatedTemplate.placeholders) : []
    };

    return NextResponse.json({
      message: 'Template updated successfully',
      template: templateWithParsedPlaceholders
    });
  } catch (error) {
    console.error('Error updating template:', error);
    return NextResponse.json(
      { error: 'Failed to update template' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/templates/[id] - Delete a template
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const templateId = parseInt(params.id);
    
    if (isNaN(templateId)) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      );
    }

    // Get the template to find its files before deletion
    const template = await getTemplateById(templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Delete the template from database first
    await deleteTemplate(templateId);

    // Try to delete associated files (non-blocking - don't fail if files don't exist)
    try {
      const templatesDir = join(process.cwd(), 'public', 'templates');
      const templateDir = join(templatesDir, template.template_name.replace(/[^a-zA-Z0-9_-]/g, '_').toLowerCase());
      
      if (existsSync(templateDir)) {
        // Delete HTML file
        const htmlFilePath = join(templateDir, template.filename);
        if (existsSync(htmlFilePath)) {
          await unlink(htmlFilePath);
        }

        // Delete images directory if it exists
        const imagesDir = join(templateDir, 'images');
        if (existsSync(imagesDir)) {
          // This is a simple approach - in production you might want to recursively delete
          try {
            await rmdir(imagesDir, { recursive: true });
          } catch (error) {
            console.warn('Could not delete images directory:', error);
          }
        }

        // Try to delete the template directory if it's empty
        try {
          await rmdir(templateDir);
        } catch (error) {
          console.warn('Could not delete template directory (may not be empty):', error);
        }
      }
    } catch (fileError) {
      console.warn('Error deleting template files:', fileError);
      // Don't fail the API call if file deletion fails
    }

    return NextResponse.json({
      message: 'Template deleted successfully',
      templateId
    });
  } catch (error) {
    console.error('Error deleting template:', error);
    return NextResponse.json(
      { error: 'Failed to delete template' },
      { status: 500 }
    );
  }
}
