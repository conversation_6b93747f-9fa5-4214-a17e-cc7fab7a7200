"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  DialogDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import { toast } from "sonner";

interface Template {
  id: number;
  template_name: string;
  description?: string;
  filename: string;
  placeholders: string[];
  layout_size?: string;
  uploaded_at: string;
  user_id: number;
  username?: string;
}

interface DeleteTemplateDialogProps {
  template: Template | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTemplateDeleted: (templateId: number) => void;
}

export function DeleteTemplateDialog({
  template,
  open,
  onOpenChange,
  onTemplateDeleted,
}: DeleteTemplateDialogProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Handle delete confirmation
  const handleDelete = async () => {
    if (!template) return;

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`/api/templates/${template.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete template");
      }

      // Notify parent component that template was deleted
      onTemplateDeleted(template.id);
      
      // Close the dialog
      onOpenChange(false);
      
      toast.success(`Template "${template.template_name}" deleted successfully`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to delete template";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    if (!loading) {
      onOpenChange(false);
      setError("");
    }
  };

  if (!template) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            Delete Template
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the template
            and remove all associated files from the server.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Template Details */}
          <div className="rounded-lg border p-4 bg-muted/50">
            <h4 className="font-semibold mb-2">Template to be deleted:</h4>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Name:</span>{" "}
                <span className="text-muted-foreground">{template.template_name}</span>
              </div>
              <div>
                <span className="font-medium">Filename:</span>{" "}
                <code className="text-xs bg-background px-1 py-0.5 rounded">
                  {template.filename}
                </code>
              </div>
              {template.description && (
                <div>
                  <span className="font-medium">Description:</span>{" "}
                  <span className="text-muted-foreground">{template.description}</span>
                </div>
              )}
              <div>
                <span className="font-medium">Placeholders:</span>{" "}
                {template.placeholders.length > 0 ? (
                  <span className="text-muted-foreground">
                    {template.placeholders.length} placeholder{template.placeholders.length !== 1 ? 's' : ''}
                  </span>
                ) : (
                  <span className="text-muted-foreground italic">None</span>
                )}
              </div>
              <div>
                <span className="font-medium">Layout Size:</span>{" "}
                <span className="text-muted-foreground">{template.layout_size || "A4"}</span>
              </div>
              {template.username && (
                <div>
                  <span className="font-medium">Uploaded by:</span>{" "}
                  <span className="text-muted-foreground">{template.username}</span>
                </div>
              )}
            </div>
          </div>

          {/* Warning Message */}
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Warning:</strong> Deleting this template will:
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Permanently remove the template from the database</li>
                <li>Delete the associated HTML file and any images</li>
                <li>Make the template unavailable for document generation</li>
              </ul>
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={loading}
            className="flex items-center gap-2"
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            {loading ? "Deleting..." : "Delete Template"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
